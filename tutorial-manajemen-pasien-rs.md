# Tutorial Aplikasi Manajemen Pasien Rumah <PERSON>t dengan <PERSON>

## Daftar Isi
1. [Persiapan dan <PERSON>asi](#persiapan-dan-instalasi)
2. [Struktur Database](#struktur-database)
3. [Model dan Relasi](#model-dan-relasi)
4. [Authentication dan Authorization](#authentication-dan-authorization)
5. [Controller dan Routes](#controller-dan-routes)
6. [Views dan UI](#views-dan-ui)
7. [Fitur Lanjutan](#fitur-lanjutan)
8. [Testing dan Deployment](#testing-dan-deployment)

## Persiapan dan Instalasi

### 1. Instalasi Laravel
```bash
composer create-project laravel/laravel hospital-management
cd hospital-management
```

### 2. Konfigurasi Database
```bash
# .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hospital_management
DB_USERNAME=root
DB_PASSWORD=
```

### 3. Install Dependencies
```bash
composer require laravel/ui
npm install && npm run dev
php artisan ui bootstrap --auth
```

## Struktur Database

### 1. Migration untuk Tabel Users (Extend)
```php
<?php
// database/migrations/add_fields_to_users_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToUsersTable extends Migration
{
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('role')->default('staff'); // admin, doctor, nurse, staff
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('specialization')->nullable(); // untuk dokter
            $table->string('license_number')->nullable(); // nomor izin praktik
            $table->boolean('is_active')->default(true);
        });
    }

    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['role', 'phone', 'address', 'birth_date', 'gender', 'specialization', 'license_number', 'is_active']);
        });
    }
}
```

### 2. Migration untuk Tabel Patients
```php
<?php
// database/migrations/create_patients_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePatientsTable extends Migration
{
    public function up()
    {
        Schema::create('patients', function (Blueprint $table) {
            $table->id();
            $table->string('patient_id')->unique(); // ID pasien unik
            $table->string('name');
            $table->enum('gender', ['male', 'female']);
            $table->date('birth_date');
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->text('address');
            $table->string('emergency_contact_name');
            $table->string('emergency_contact_phone');
            $table->string('blood_type')->nullable();
            $table->text('allergies')->nullable();
            $table->text('medical_history')->nullable();
            $table->string('insurance_number')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('patients');
    }
}
```

### 3. Migration untuk Tabel Appointments
```php
<?php
// database/migrations/create_appointments_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAppointmentsTable extends Migration
{
    public function up()
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->string('appointment_number')->unique();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('users')->onDelete('cascade');
            $table->datetime('appointment_date');
            $table->enum('status', ['scheduled', 'completed', 'cancelled', 'no_show'])->default('scheduled');
            $table->text('reason')->nullable();
            $table->text('notes')->nullable();
            $table->decimal('consultation_fee', 10, 2)->default(0);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('appointments');
    }
}
```

### 4. Migration untuk Tabel Medical Records
```php
<?php
// database/migrations/create_medical_records_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMedicalRecordsTable extends Migration
{
    public function up()
    {
        Schema::create('medical_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->date('visit_date');
            $table->text('chief_complaint'); // keluhan utama
            $table->text('history_of_present_illness'); // riwayat penyakit sekarang
            $table->text('physical_examination'); // pemeriksaan fisik
            $table->string('vital_signs')->nullable(); // tanda vital
            $table->text('diagnosis'); // diagnosis
            $table->text('treatment_plan'); // rencana pengobatan
            $table->text('prescription')->nullable(); // resep obat
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('medical_records');
    }
}
```

### 5. Migration untuk Tabel Prescriptions
```php
<?php
// database/migrations/create_prescriptions_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePrescriptionsTable extends Migration
{
    public function up()
    {
        Schema::create('prescriptions', function (Blueprint $table) {
            $table->id();
            $table->string('prescription_number')->unique();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('medical_record_id')->nullable()->constrained()->onDelete('set null');
            $table->date('prescription_date');
            $table->text('medications'); // JSON format untuk list obat
            $table->text('instructions');
            $table->enum('status', ['active', 'completed', 'cancelled'])->default('active');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('prescriptions');
    }
}
```

## Model dan Relasi

### 1. Model User (Extended)
```php
<?php
// app/Models/User.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name', 'email', 'password', 'role', 'phone', 'address', 
        'birth_date', 'gender', 'specialization', 'license_number', 'is_active'
    ];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'birth_date' => 'date',
        'is_active' => 'boolean',
    ];

    // Relasi
    public function appointmentsAsDoctor()
    {
        return $this->hasMany(Appointment::class, 'doctor_id');
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class, 'doctor_id');
    }

    public function prescriptions()
    {
        return $this->hasMany(Prescription::class, 'doctor_id');
    }

    // Scope
    public function scopeDoctors($query)
    {
        return $query->where('role', 'doctor');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Accessor
    public function getAgeAttribute()
    {
        return $this->birth_date ? $this->birth_date->diffInYears(now()) : null;
    }
}
```

### 2. Model Patient
```php
<?php
// app/Models/Patient.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Patient extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id', 'name', 'gender', 'birth_date', 'phone', 'email', 
        'address', 'emergency_contact_name', 'emergency_contact_phone', 
        'blood_type', 'allergies', 'medical_history', 'insurance_number', 'is_active'
    ];

    protected $casts = [
        'birth_date' => 'date',
        'is_active' => 'boolean',
    ];

    // Relasi
    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class);
    }

    public function prescriptions()
    {
        return $this->hasMany(Prescription::class);
    }

    // Accessor
    public function getAgeAttribute()
    {
        return $this->birth_date->diffInYears(now());
    }

    // Boot method untuk auto-generate patient ID
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($patient) {
            $patient->patient_id = 'P' . str_pad(Patient::count() + 1, 6, '0', STR_PAD_LEFT);
        });
    }
}
```

### 3. Model Appointment
```php
<?php
// app/Models/Appointment.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'appointment_number', 'patient_id', 'doctor_id', 'appointment_date', 
        'status', 'reason', 'notes', 'consultation_fee'
    ];

    protected $casts = [
        'appointment_date' => 'datetime',
        'consultation_fee' => 'decimal:2',
    ];

    // Relasi
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function medicalRecord()
    {
        return $this->hasOne(MedicalRecord::class);
    }

    // Boot method
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($appointment) {
            $appointment->appointment_number = 'APT' . date('Ymd') . str_pad(Appointment::whereDate('created_at', today())->count() + 1, 3, '0', STR_PAD_LEFT);
        });
    }
}
```

### 4. Model MedicalRecord
```php
<?php
// app/Models/MedicalRecord.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MedicalRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id', 'doctor_id', 'appointment_id', 'visit_date', 
        'chief_complaint', 'history_of_present_illness', 'physical_examination', 
        'vital_signs', 'diagnosis', 'treatment_plan', 'prescription', 'notes'
    ];

    protected $casts = [
        'visit_date' => 'date',
    ];

    // Relasi
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }
}
```

## Authentication dan Authorization

### 1. Middleware untuk Role-based Access
```php
<?php
// app/Http/Middleware/RoleMiddleware.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RoleMiddleware
{
    public function handle(Request $request, Closure $next, ...$roles)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        if (!in_array(auth()->user()->role, $roles)) {
            abort(403, 'Unauthorized action.');
        }

        return $next($request);
    }
}
```

### 2. Register Middleware
```php
<?php
// app/Http/Kernel.php
protected $routeMiddleware = [
    // ... existing middleware
    'role' => \App\Http\Middleware\RoleMiddleware::class,
];
```

### 3. Service Provider untuk Gates
```php
<?php
// app/Providers/AuthServiceProvider.php
namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->registerPolicies();

        // Gate untuk admin
        Gate::define('admin-access', function ($user) {
            return $user->role === 'admin';
        });

        // Gate untuk dokter
        Gate::define('doctor-access', function ($user) {
            return in_array($user->role, ['admin', 'doctor']);
        });

        // Gate untuk staff
        Gate::define('staff-access', function ($user) {
            return in_array($user->role, ['admin', 'staff', 'nurse']);
        });
    }
}
```

## Controller dan Routes

### 1. Controller Dashboard
```php
<?php
// app/Http/Controllers/DashboardController.php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Patient;
use App\Models\Appointment;
use App\Models\User;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'total_patients' => Patient::count(),
            'total_doctors' => User::doctors()->count(),
            'today_appointments' => Appointment::whereDate('appointment_date', today())->count(),
            'pending_appointments' => Appointment::where('status', 'scheduled')->count(),
        ];

        $recent_appointments = Appointment::with(['patient', 'doctor'])
            ->latest()
            ->take(5)
            ->get();

        return view('dashboard', compact('stats', 'recent_appointments'));
    }
}
```

### 2. Controller Patient
```php
<?php
// app/Http/Controllers/PatientController.php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Patient;
use App\Http\Requests\PatientRequest;

class PatientController extends Controller
{
    public function index(Request $request)
    {
        $query = Patient::query();

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('patient_id', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%');
            });
        }

        $patients = $query->latest()->paginate(10);

        return view('patients.index', compact('patients'));
    }

    public function create()
    {
        return view('patients.create');
    }

    public function store(PatientRequest $request)
    {
        Patient::create($request->validated());

        return redirect()->route('patients.index')
            ->with('success', 'Patient created successfully.');
    }

    public function show(Patient $patient)
    {
        $patient->load(['appointments.doctor', 'medicalRecords.doctor']);
        
        return view('patients.show', compact('patient'));
    }

    public function edit(Patient $patient)
    {
        return view('patients.edit', compact('patient'));
    }

    public function update(PatientRequest $request, Patient $patient)
    {
        $patient->update($request->validated());

        return redirect()->route('patients.show', $patient)
            ->with('success', 'Patient updated successfully.');
    }

    public function destroy(Patient $patient)
    {
        $patient->delete();

        return redirect()->route('patients.index')
            ->with('success', 'Patient deleted successfully.');
    }
}
```

### 3. Form Request untuk Validasi
```php
<?php
// app/Http/Requests/PatientRequest.php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PatientRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'gender' => 'required|in:male,female',
            'birth_date' => 'required|date|before:today',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'blood_type' => 'nullable|string|max:10',
            'allergies' => 'nullable|string',
            'medical_history' => 'nullable|string',
            'insurance_number' => 'nullable|string|max:50',
        ];
    }
}
```

### 4. Routes
```php
<?php
// routes/web.php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\MedicalRecordController;

Auth::routes();

Route::middleware(['auth'])->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    
    // Patient routes
    Route::resource('patients', PatientController::class);
    
    // Appointment routes
    Route::resource('appointments', AppointmentController::class);
    
    // Medical Record routes (hanya untuk dokter)
    Route::middleware(['role:admin,doctor'])->group(function () {
        Route::resource('medical-records', MedicalRecordController::class);
    });
    
    // Admin only routes
    Route::middleware(['role:admin'])->group(function () {
        Route::resource('users', UserController::class);
    });
});
```

## Views dan UI

### 1. Layout Utama
```blade
{{-- resources/views/layouts/app.blade.php --}}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hospital Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ route('dashboard') }}">
                <i class="fas fa-hospital"></i> Hospital Management
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        {{ Auth::user()->name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3 col-lg-2 sidebar bg-light">
                @include('partials.sidebar')
            </div>
            
            <div class="col-md-9 col-lg-10">
                <main class="p-4">
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif
                    
                    @yield('content')
                </main>
            </div>
        </div>
    </div>

    <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
        @csrf
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
```

### 2. Sidebar
```blade
{{-- resources/views/partials/sidebar.blade.php --}}
<div class="sidebar-sticky pt-3">
    <ul class="nav flex-column">
        <li class="nav-item">
            <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>
        
        <li class="nav-item">
            <a class="nav-link {{ request()->routeIs('patients.*') ? 'active' : '' }}" href="{{ route('patients.index') }}">
                <i class="fas fa-users"></i> Patients
            </a>
        </li>
        
        <li class="nav-item">
            <a class="nav-link {{ request()->routeIs('appointments.*') ? 'active' : '' }}" href="{{ route('appointments.index') }}">
                <i class="fas fa-calendar-check"></i> Appointments
            </a>
        </li>
        
        @can('doctor-access')
        <li class="nav-item">
            <a class="nav-link {{ request()->routeIs('medical-records.*') ? 'active' : '' }}" href="{{ route('medical-records.index') }}">
                <i class="fas fa-file-medical"></i> Medical Records
            </a>
        </li>
        @endcan
        
        @can('admin-access')
        <li class="nav-item">
            <a class="nav-link {{ request()->routeIs('users.*') ? 'active' : '' }}" href="{{ route('users.index') }}">
                <i class="fas fa-user-cog"></i> Users
            </a>
        </li>
        @endcan
    </ul>
</div>
```

### 3. Dashboard View
```blade
{{-- resources/views/dashboard.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Dashboard</h1>
    <span class="text-muted">Welcome, {{ Auth::user()->name }}</span>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Patients</h5>
                        <h2>{{ $stats['total_patients'] }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Doctors</h5>
                        <h2>{{ $stats['total_doctors'] }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-md fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Today's Appointments</h5>
                        <h2>{{ $stats['today_appointments'] }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Pending Appointments</h5>
                        <h2>{{ $stats['pending_appointments'] }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title">Recent Appointments</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Appointment #</th>
                        <th>Patient</th>
                        <th>Doctor</th>
                        <th>Date</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($recent_appointments as $appointment)
                    <tr>
                        <td>{{ $appointment->appointment_number }}</td>
                        <td>{{ $appointment->patient->name }}</td>
                        <td>{{ $appointment->doctor->name }}</td>
                        <td>{{ $appointment->appointment_date->format('d/m/Y H:i') }}</td>
                        <td>
                            <span class="badge bg-{{ $appointment->status == 'completed' ? 'success' : ($appointment->status == 'cancelled' ? 'danger' : 'primary') }}">
                                {{ ucfirst($appointment->status) }}
                            </span>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center">No recent appointments</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection
```

### 4. Patient Index View
```blade
{{-- resources/views/patients/index.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Patients</h1>
    <a href="{{ route('patients.create') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Patient
    </a>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="card-title mb-0">Patient List</h5>
            </div>
            <div class="col-md-6">
                <form method="GET" action="{{ route('patients.index') }}">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="Search patients..." value="{{ request('search') }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Patient ID</th>
                        <th>Name</th>
                        <th>Gender</th>
                        <th>Age</th>
                        <th>Phone</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($patients as $patient)
                    <tr>
                        <td>{{ $patient->patient_id }}</td>
                        <td>{{ $patient->name }}</td>
                        <td>{{ ucfirst($patient->gender) }}</td>
                        <td>{{ $patient->age }} years</td>
                        <td>{{ $patient->phone ?: '-' }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('patients.show', $patient) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('patients.edit', $patient) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ route('patients.destroy', $patient) }}" style="display: inline;">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="text-center">No patients found</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <div class="d-flex justify-content-center">
            {{ $patients->links() }}
        </div>
    </div>
</div>
@endsection
```

### 5. Patient Create Form
```blade
{{-- resources/views/patients/create.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Add New Patient</h1>
    <a href="{{ route('patients.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Patients
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title">Patient Information</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('patients.store') }}">
            @csrf
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" 
                               value="{{ old('name') }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                        <select name="gender" class="form-select @error('gender') is-invalid @enderror" required>
                            <option value="">Select Gender</option>
                            <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>Male</option>
                            <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>Female</option>
                        </select>
                        @error('gender')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="birth_date" class="form-label">Birth Date <span class="text-danger">*</span></label>
                        <input type="date" name="birth_date" class="form-control @error('birth_date') is-invalid @enderror" 
                               value="{{ old('birth_date') }}" required>
                        @error('birth_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="text" name="phone" class="form-control @error('phone') is-invalid @enderror" 
                               value="{{ old('phone') }}">
                        @error('phone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" name="email" class="form-control @error('email') is-invalid @enderror" 
                               value="{{ old('email') }}">
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                <textarea name="address" class="form-control @error('address') is-invalid @enderror" 
                          rows="3" required>{{ old('address') }}</textarea>
                @error('address')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emergency_contact_name" class="form-label">Emergency Contact Name <span class="text-danger">*</span></label>
                        <input type="text" name="emergency_contact_name" class="form-control @error('emergency_contact_name') is-invalid @enderror" 
                               value="{{ old('emergency_contact_name') }}" required>
                        @error('emergency_contact_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emergency_contact_phone" class="form-label">Emergency Contact Phone <span class="text-danger">*</span></label>
                        <input type="text" name="emergency_contact_phone" class="form-control @error('emergency_contact_phone') is-invalid @enderror" 
                               value="{{ old('emergency_contact_phone') }}" required>
                        @error('emergency_contact_phone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="blood_type" class="form-label">Blood Type</label>
                        <select name="blood_type" class="form-select @error('blood_type') is-invalid @enderror">
                            <option value="">Select Blood Type</option>
                            <option value="A+" {{ old('blood_type') == 'A+' ? 'selected' : '' }}>A+</option>
                            <option value="A-" {{ old('blood_type') == 'A-' ? 'selected' : '' }}>A-</option>
                            <option value="B+" {{ old('blood_type') == 'B+' ? 'selected' : '' }}>B+</option>
                            <option value="B-" {{ old('blood_type') == 'B-' ? 'selected' : '' }}>B-</option>
                            <option value="AB+" {{ old('blood_type') == 'AB+' ? 'selected' : '' }}>AB+</option>
                            <option value="AB-" {{ old('blood_type') == 'AB-' ? 'selected' : '' }}>AB-</option>
                            <option value="O+" {{ old('blood_type') == 'O+' ? 'selected' : '' }}>O+</option>
                            <option value="O-" {{ old('blood_type') == 'O-' ? 'selected' : '' }}>O-</option>
                        </select>
                        @error('blood_type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="insurance_number" class="form-label">Insurance Number</label>
                        <input type="text" name="insurance_number" class="form-control @error('insurance_number') is-invalid @enderror" 
                               value="{{ old('insurance_number') }}">
                        @error('insurance_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="allergies" class="form-label">Allergies</label>
                <textarea name="allergies" class="form-control @error('allergies') is-invalid @enderror" 
                          rows="2" placeholder="List any known allergies...">{{ old('allergies') }}</textarea>
                @error('allergies')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="mb-3">
                <label for="medical_history" class="form-label">Medical History</label>
                <textarea name="medical_history" class="form-control @error('medical_history') is-invalid @enderror" 
                          rows="3" placeholder="Previous medical conditions, surgeries, etc...">{{ old('medical_history') }}</textarea>
                @error('medical_history')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Patient
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
```

## Fitur Lanjutan

### 1. Appointment Controller
```php
<?php
// app/Http/Controllers/AppointmentController.php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Appointment;
use App\Models\Patient;
use App\Models\User;
use App\Http\Requests\AppointmentRequest;

class AppointmentController extends Controller
{
    public function index(Request $request)
    {
        $query = Appointment::with(['patient', 'doctor']);

        if ($request->filled('date')) {
            $query->whereDate('appointment_date', $request->date);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('doctor_id')) {
            $query->where('doctor_id', $request->doctor_id);
        }

        $appointments = $query->latest('appointment_date')->paginate(15);
        $doctors = User::doctors()->active()->get();

        return view('appointments.index', compact('appointments', 'doctors'));
    }

    public function create()
    {
        $patients = Patient::active()->orderBy('name')->get();
        $doctors = User::doctors()->active()->get();

        return view('appointments.create', compact('patients', 'doctors'));
    }

    public function store(AppointmentRequest $request)
    {
        // Check for scheduling conflicts
        $existingAppointment = Appointment::where('doctor_id', $request->doctor_id)
            ->where('appointment_date', $request->appointment_date)
            ->where('status', 'scheduled')
            ->first();

        if ($existingAppointment) {
            return back()->withErrors(['appointment_date' => 'Doctor already has an appointment at this time.']);
        }

        Appointment::create($request->validated());

        return redirect()->route('appointments.index')
            ->with('success', 'Appointment scheduled successfully.');
    }

    public function show(Appointment $appointment)
    {
        $appointment->load(['patient', 'doctor', 'medicalRecord']);
        
        return view('appointments.show', compact('appointment'));
    }

    public function updateStatus(Request $request, Appointment $appointment)
    {
        $request->validate([
            'status' => 'required|in:scheduled,completed,cancelled,no_show',
            'notes' => 'nullable|string'
        ]);

        $appointment->update([
            'status' => $request->status,
            'notes' => $request->notes
        ]);

        return back()->with('success', 'Appointment status updated successfully.');
    }
}
```

### 2. Medical Record Controller
```php
<?php
// app/Http/Controllers/MedicalRecordController.php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MedicalRecord;
use App\Models\Patient;
use App\Models\Appointment;
use App\Http\Requests\MedicalRecordRequest;

class MedicalRecordController extends Controller
{
    public function index(Request $request)
    {
        $query = MedicalRecord::with(['patient', 'doctor']);

        if ($request->filled('patient_id')) {
            $query->where('patient_id', $request->patient_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('visit_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('visit_date', '<=', $request->date_to);
        }

        $medicalRecords = $query->latest('visit_date')->paginate(15);
        $patients = Patient::active()->orderBy('name')->get();

        return view('medical-records.index', compact('medicalRecords', 'patients'));
    }

    public function create(Request $request)
    {
        $appointment = null;
        if ($request->filled('appointment_id')) {
            $appointment = Appointment::with(['patient', 'doctor'])->find($request->appointment_id);
        }

        $patients = Patient::active()->orderBy('name')->get();

        return view('medical-records.create', compact('appointment', 'patients'));
    }

    public function store(MedicalRecordRequest $request)
    {
        $medicalRecord = MedicalRecord::create($request->validated());

        // Update appointment status if linked
        if ($request->appointment_id) {
            Appointment::find($request->appointment_id)->update(['status' => 'completed']);
        }

        return redirect()->route('medical-records.show', $medicalRecord)
            ->with('success', 'Medical record created successfully.');
    }

    public function show(MedicalRecord $medicalRecord)
    {
        $medicalRecord->load(['patient', 'doctor', 'appointment']);
        
        return view('medical-records.show', compact('medicalRecord'));
    }

    public function print(MedicalRecord $medicalRecord)
    {
        $medicalRecord->load(['patient', 'doctor']);
        
        return view('medical-records.print', compact('medicalRecord'));
    }
}
```

### 3. Seeder untuk Data Dummy
```php
<?php
// database/seeders/DatabaseSeeder.php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Patient;
use App\Models\Appointment;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'phone' => '08**********',
            'is_active' => true,
        ]);

        // Create doctors
        User::create([
            'name' => 'Dr. John Smith',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'doctor',
            'phone' => '081234567891',
            'specialization' => 'General Medicine',
            'license_number' => 'DOC001',
            'is_active' => true,
        ]);

        User::create([
            'name' => 'Dr. Sarah Johnson',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'doctor',
            'phone' => '081234567892',
            'specialization' => 'Cardiology',
            'license_number' => 'DOC002',
            'is_active' => true,
        ]);

        // Create sample patients
        Patient::factory(50)->create();

        // Create sample appointments
        Appointment::factory(30)->create();
    }
}
```

### 4. Factory untuk Model
```php
<?php
// database/factories/PatientFactory.php
namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class PatientFactory extends Factory
{
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'gender' => $this->faker->randomElement(['male', 'female']),
            'birth_date' => $this->faker->dateTimeBetween('-80 years', '-18 years'),
            'phone' => $this->faker->phoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'address' => $this->faker->address(),
            'emergency_contact_name' => $this->faker->name(),
            'emergency_contact_phone' => $this->faker->phoneNumber(),
            'blood_type' => $this->faker->randomElement(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']),
            'allergies' => $this->faker->optional()->sentence(),
            'medical_history' => $this->faker->optional()->paragraph(),
            'insurance_number' => $this->faker->optional()->numerify('INS-########'),
        ];
    }
}
```

## Fitur Tambahan

### 1. Export ke PDF
```php
// Install dompdf
composer require barryvdh/laravel-dompdf

// Controller method
public function exportPdf(Patient $patient)
{
    $pdf = PDF::loadView('patients.pdf', compact('patient'));
    
    return $pdf->download('patient-' . $patient->patient_id . '.pdf');
}
```

### 2. Search dan Filter Advanced
```php
// app/Http/Controllers/PatientController.php
public function search(Request $request)
{
    $query = Patient::query();

    if ($request->filled('name')) {
        $query->where('name', 'like', '%' . $request->name . '%');
    }

    if ($request->filled('patient_id')) {
        $query->where('patient_id', 'like', '%' . $request->patient_id . '%');
    }

    if ($request->filled('gender')) {
        $query->where('gender', $request->gender);
    }

    if ($request->filled('blood_type')) {
        $query->where('blood_type', $request->blood_type);
    }

    if ($request->filled('age_from') && $request->filled('age_to')) {
        $query->whereBetween('birth_date', [
            now()->subYears($request->age_to)->format('Y-m-d'),
            now()->subYears($request->age_from)->format('Y-m-d')
        ]);
    }

    $patients = $query->paginate(15);

    return view('patients.search', compact('patients'));
}
```

### 3. Notification System
```php
// app/Notifications/AppointmentReminder.php
namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class AppointmentReminder extends Notification
{
    use Queueable;

    protected $appointment;

    public function __construct($appointment)
    {
        $this->appointment = $appointment;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line('You have an appointment reminder.')
            ->line('Date: ' . $this->appointment->appointment_date->format('d/m/Y H:i'))
            ->line('Doctor: ' . $this->appointment->doctor->name)
            ->action('View Appointment', url('/appointments/' . $this->appointment->id))
            ->line('Thank you for using our hospital management system!');
    }
}
```

### 4. Command untuk Backup Database
```php
// app/Console/Commands/BackupDatabase.php
namespace App\Console\Commands;

use Illuminate\Console\Command;

class BackupDatabase extends Command
{
    protected $signature = 'backup:database';
    protected $description = 'Backup the database';

    public function handle()
    {
        $filename = 'backup-' . date('Y-m-d-H-i-s') . '.sql';
        $path = storage_path('app/backups/' . $filename);

        $command = sprintf(
            'mysqldump -u%s -p%s %s > %s',
            config('database.connections.mysql.username'),
            config('database.connections.mysql.password'),
            config('database.connections.mysql.database'),
            $path
        );

        exec($command);

        $this->info('Database backup created: ' . $filename);
    }
}
```

## Testing dan Deployment

### 1. Unit Testing
```php
<?php
// tests/Unit/PatientTest.php
namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Patient;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PatientTest extends TestCase
{
    use RefreshDatabase;

    public function test_patient_can_be_created()
    {
        $patient = Patient::factory()->create();

        $this->assertDatabaseHas('patients', [
            'name' => $patient->name,
            'email' => $patient->email,
        ]);
    }

    public function test_patient_age_calculation()
    {
        $patient = Patient::factory()->create([
            'birth_date' => now()->subYears(25)
        ]);

        $this->assertEquals(25, $patient->age);
    }
}
```

### 2. Feature Testing
```php
<?php
// tests/Feature/PatientControllerTest.php
namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Patient;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PatientControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_authenticated_user_can_view_patients()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $response = $this->get('/patients');

        $response->assertStatus(200);
    }

    public function test_patient_can_be_created()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $patientData = [
            'name' => 'John Doe',
            'gender' => 'male',
            'birth_date' => '1990-01-01',
            'address' => '123 Main St',
            'emergency_contact_name' => 'Jane Doe',
            'emergency_contact_phone' => '**********',
        ];

        $response = $this->post('/patients', $patientData);

        $response->assertRedirect('/patients');
        $this->assertDatabaseHas('patients', ['name' => 'John Doe']);
    }
}
```

### 3. Deployment dengan Docker
```dockerfile
# Dockerfile
FROM php:8.1-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy existing application directory contents
COPY . /var/www

# Install dependencies
RUN composer install --no-dev --optimize-autoloader

# Set permissions
RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/storage

EXPOSE 9000
CMD ["php-fpm"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "9000:9000"
    volumes:
      - .:/var/www
    depends_on:
      - db
    environment:
      - DB_HOST=db
      - DB_DATABASE=hospital_management
      - DB_USERNAME=root
      - DB_PASSWORD=secret

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - .:/var/www
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app

  db:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: hospital_management
    volumes:
      - db_data:/var/lib/mysql

volumes:
  db_data:
```

### 4. Optimasi Performance
```php
// config/cache.php - Redis configuration
'redis' => [
    'client' => env('REDIS_CLIENT', 'phpredis'),
    'options' => [
        'cluster' => env('REDIS_CLUSTER', 'redis'),
        'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
    ],
    'default' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD', null),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_DB', '0'),
    ],
],
```

## Kesimpulan

Aplikasi manajemen pasien rumah sakit ini mencakup:

1. **Manajemen Pasien**: CRUD lengkap dengan validasi
2. **Sistem Appointment**: Penjadwalan dengan conflict detection
3. **Medical Records**: Rekam medis elektronik
4. **Authentication & Authorization**: Role-based access control
5. **Dashboard**: Statistik dan overview
6. **Export/Print**: Cetak laporan dan data
7. **Search & Filter**: Pencarian advanced
8. **Notification**: Reminder sistem
9. **Backup**: Automated database backup
10. **Testing**: Unit dan feature testing
11. **Deployment**: Docker containerization

Aplikasi ini siap untuk dikembangkan lebih lanjut dengan fitur tambahan seperti:
- Sistem billing dan invoice
- Integrasi dengan peralatan medis
- Telemedicine features
- Mobile app dengan API
- Reporting dan analytics
- Inventory management untuk obat-obatan

Pastikan untuk selalu mengikuti best practices dalam pengembangan, keamanan data pasien, dan compliance dengan regulasi kesehatan yang berlaku.